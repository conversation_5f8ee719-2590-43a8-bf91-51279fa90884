// ملف المميزات المحسنة لإضافة أذكار المسلم

// 1. وضع ليلي/نهاري
function initThemeToggle() {
  const themeToggle = document.createElement('div');
  themeToggle.className = 'theme-toggle';
  themeToggle.innerHTML = `
    <button id="theme-btn" class="theme-btn" title="تبديل الوضع الليلي/النهاري">
      🌙
    </button>
  `;
  
  document.querySelector('header').appendChild(themeToggle);
  
  // تحميل الثيم المحفوظ
  chrome.storage.local.get(['theme'], (result) => {
    const theme = result.theme || 'light';
    applyTheme(theme);
  });
  
  // إضافة مستمع للنقر
  document.getElementById('theme-btn').addEventListener('click', toggleTheme);
}

function toggleTheme() {
  const currentTheme = document.body.classList.contains('dark-theme') ? 'dark' : 'light';
  const newTheme = currentTheme === 'light' ? 'dark' : 'light';
  
  applyTheme(newTheme);
  chrome.storage.local.set({ theme: newTheme });
}

function applyTheme(theme) {
  const themeBtn = document.getElementById('theme-btn');
  
  if (theme === 'dark') {
    document.body.classList.add('dark-theme');
    if (themeBtn) themeBtn.textContent = '☀️';
  } else {
    document.body.classList.remove('dark-theme');
    if (themeBtn) themeBtn.textContent = '🌙';
  }
}

// 2. إحصائيات بسيطة
function initStatistics() {
  // تتبع عدد مرات قراءة الأذكار
  chrome.storage.local.get(['azkarStats'], (result) => {
    const stats = result.azkarStats || {
      totalReads: 0,
      dailyReads: 0,
      lastDate: new Date().toDateString()
    };
    
    // إعادة تعيين الإحصائيات اليومية إذا كان يوم جديد
    const today = new Date().toDateString();
    if (stats.lastDate !== today) {
      stats.dailyReads = 0;
      stats.lastDate = today;
      chrome.storage.local.set({ azkarStats: stats });
    }
    
    updateStatsDisplay(stats);
  });
}

function updateStatsDisplay(stats) {
  const welcomeDiv = document.querySelector('.welcome');
  if (welcomeDiv && !document.getElementById('stats-display')) {
    const statsDiv = document.createElement('div');
    statsDiv.id = 'stats-display';
    statsDiv.className = 'stats-display';
    statsDiv.innerHTML = `
      <h3>📊 إحصائياتك</h3>
      <p>قراءات اليوم: <span class="stat-number">${stats.dailyReads}</span></p>
      <p>إجمالي القراءات: <span class="stat-number">${stats.totalReads}</span></p>
    `;
    welcomeDiv.appendChild(statsDiv);
  }
}

function incrementReadCount() {
  chrome.storage.local.get(['azkarStats'], (result) => {
    const stats = result.azkarStats || {
      totalReads: 0,
      dailyReads: 0,
      lastDate: new Date().toDateString()
    };
    
    stats.totalReads++;
    stats.dailyReads++;
    
    chrome.storage.local.set({ azkarStats: stats });
    updateStatsDisplay(stats);
  });
}

// 3. تحسين العداد مع الإحصائيات
function enhancedIncrementCounter(display, maxCount) {
  const currentText = display.textContent;
  const currentCount = parseInt(currentText.split(' / ')[0]);
  
  if (currentCount < maxCount) {
    const newCount = currentCount + 1;
    display.textContent = `${newCount} / ${maxCount}`;
    
    // إضافة إلى الإحصائيات
    incrementReadCount();
    
    if (newCount === maxCount) {
      display.style.color = '#4CAF50';
      display.style.fontWeight = 'bold';
      
      // إضافة تأثير بصري للإكمال
      display.parentElement.classList.add('completed');
      setTimeout(() => {
        display.parentElement.classList.remove('completed');
      }, 2000);
    }
  }
}

// 4. تحسين الإشعارات مع الصوت
function enhancedShowNotification(title, message, audioPath = null) {
  // إشعار Chrome
  chrome.notifications.create({
    type: 'basic',
    iconUrl: 'images/icon128.png',
    title: title,
    message: message,
    priority: 2,
    buttons: [
      { title: 'عرض الأذكار' },
      { title: 'إغلاق' }
    ]
  });
  
  // تشغيل صوت الإشعار إذا كان متاح
  if (audioPath && audioEnabled) {
    playAudio(audioPath);
  }
}

// 5. تصدير/استيراد الإعدادات
function initSettingsBackup() {
  const settingsForm = document.querySelector('.settings-form');
  if (settingsForm) {
    const backupDiv = document.createElement('div');
    backupDiv.className = 'backup-controls';
    backupDiv.innerHTML = `
      <h3>📁 نسخ احتياطي للإعدادات</h3>
      <button id="export-settings" class="btn secondary">تصدير الإعدادات</button>
      <button id="import-settings" class="btn secondary">استيراد الإعدادات</button>
      <input type="file" id="import-file" accept=".json" style="display: none;">
    `;
    
    settingsForm.appendChild(backupDiv);
    
    document.getElementById('export-settings').addEventListener('click', exportSettings);
    document.getElementById('import-settings').addEventListener('click', () => {
      document.getElementById('import-file').click();
    });
    document.getElementById('import-file').addEventListener('change', importSettings);
  }
}

function exportSettings() {
  chrome.storage.local.get(['azkarTimes', 'audioSettings', 'theme', 'azkarStats'], (result) => {
    const settings = {
      azkarTimes: result.azkarTimes,
      audioSettings: result.audioSettings,
      theme: result.theme,
      azkarStats: result.azkarStats,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `azkar-settings-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
  });
}

function importSettings(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const settings = JSON.parse(e.target.result);
      
      chrome.storage.local.set(settings, () => {
        alert('تم استيراد الإعدادات بنجاح! سيتم إعادة تحميل الإضافة.');
        location.reload();
      });
    } catch (error) {
      alert('خطأ في قراءة ملف الإعدادات. تأكد من صحة الملف.');
    }
  };
  reader.readAsText(file);
}

// 6. تهيئة جميع المميزات المحسنة
function initEnhancedFeatures() {
  // تأخير قصير للتأكد من تحميل DOM
  setTimeout(() => {
    initThemeToggle();
    initStatistics();
    initSettingsBackup();
  }, 500);
}

// تصدير الوظائف للاستخدام في popup.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    initEnhancedFeatures,
    enhancedIncrementCounter,
    enhancedShowNotification
  };
}
