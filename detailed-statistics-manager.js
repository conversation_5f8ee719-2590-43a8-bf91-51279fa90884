// مدير الإحصائيات المفصلة
class DetailedStatisticsManager {
  constructor() {
    this.stats = {};
    this.charts = {};
  }

  // تهيئة مدير الإحصائيات
  async init() {
    await this.loadStatistics();
    this.setupStatisticsUI();
    this.startDataCollection();
  }

  // تحميل الإحصائيات
  async loadStatistics() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['detailedStats'], (result) => {
        this.stats = result.detailedStats || {
          dailyReads: {},
          weeklyReads: {},
          monthlyReads: {},
          categoryStats: {},
          timeStats: {},
          streakData: { current: 0, longest: 0, lastReadDate: null },
          achievements: [],
          totalReadingTime: 0,
          favoriteAzkar: {},
          readingPatterns: {}
        };
        resolve();
      });
    });
  }

  // حفظ الإحصائيات
  async saveStatistics() {
    return new Promise((resolve) => {
      chrome.storage.local.set({ detailedStats: this.stats }, resolve);
    });
  }

  // إعداد واجهة الإحصائيات
  setupStatisticsUI() {
    this.createStatisticsSection();
    this.createChartsSection();
    this.createAchievementsSection();
  }

  // إنشاء قسم الإحصائيات
  createStatisticsSection() {
    const container = document.getElementById('statistics-container');
    if (!container) return;

    container.innerHTML = `
      <div class="statistics-header">
        <h3>📊 إحصائياتك المفصلة</h3>
        <button id="refresh-stats-btn" class="btn-secondary">تحديث</button>
      </div>
      
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📿</div>
          <div class="stat-content">
            <h4 id="total-reads">0</h4>
            <p>إجمالي الأذكار</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🔥</div>
          <div class="stat-content">
            <h4 id="current-streak">0</h4>
            <p>أيام متتالية</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⏱️</div>
          <div class="stat-content">
            <h4 id="reading-time">0</h4>
            <p>دقائق قراءة</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🏆</div>
          <div class="stat-content">
            <h4 id="achievements-count">0</h4>
            <p>إنجازات</p>
          </div>
        </div>
      </div>
      
      <div class="detailed-stats">
        <div class="stat-section">
          <h4>📈 إحصائيات الأسبوع</h4>
          <div id="weekly-chart" class="chart-container"></div>
        </div>
        
        <div class="stat-section">
          <h4>📊 الأذكار المفضلة</h4>
          <div id="favorite-azkar-list" class="favorite-list"></div>
        </div>
        
        <div class="stat-section">
          <h4>⏰ أنماط القراءة</h4>
          <div id="reading-patterns" class="patterns-container"></div>
        </div>
      </div>
    `;

    // ربط الأحداث
    document.getElementById('refresh-stats-btn')?.addEventListener('click', () => {
      this.updateStatistics();
    });
  }

  // إنشاء قسم الرسوم البيانية
  createChartsSection() {
    this.createWeeklyChart();
    this.createCategoryChart();
    this.createTimePatternChart();
  }

  // إنشاء رسم بياني أسبوعي
  createWeeklyChart() {
    const container = document.getElementById('weekly-chart');
    if (!container) return;

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '200');
    svg.setAttribute('viewBox', '0 0 350 200');
    svg.setAttribute('class', 'weekly-chart');

    container.appendChild(svg);
    this.charts.weekly = svg;

    this.updateWeeklyChart();
  }

  // تحديث الرسم البياني الأسبوعي
  updateWeeklyChart() {
    const svg = this.charts.weekly;
    if (!svg) return;

    // مسح المحتوى السابق
    svg.innerHTML = '';

    // الحصول على بيانات الأسبوع
    const weekData = this.getWeeklyData();
    
    // رسم الشبكة
    this.drawGrid(svg, 350, 200);
    
    // رسم البيانات
    this.drawWeeklyBars(svg, weekData);
    
    // رسم التسميات
    this.drawWeeklyLabels(svg, weekData);
  }

  // رسم الشبكة
  drawGrid(svg, width, height) {
    // خطوط أفقية
    for (let i = 0; i <= 5; i++) {
      const y = 30 + (i * 30);
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', '40');
      line.setAttribute('y1', y);
      line.setAttribute('x2', width - 20);
      line.setAttribute('y2', y);
      line.setAttribute('stroke', '#e0e0e0');
      line.setAttribute('stroke-width', '1');
      svg.appendChild(line);
    }
  }

  // رسم أعمدة الأسبوع
  drawWeeklyBars(svg, data) {
    const barWidth = 35;
    const maxHeight = 120;
    const maxValue = Math.max(...data.map(d => d.count), 1);

    data.forEach((day, index) => {
      const x = 50 + (index * 45);
      const height = (day.count / maxValue) * maxHeight;
      const y = 150 - height;

      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      rect.setAttribute('x', x);
      rect.setAttribute('y', y);
      rect.setAttribute('width', barWidth);
      rect.setAttribute('height', height);
      rect.setAttribute('fill', this.getBarColor(day.count, maxValue));
      rect.setAttribute('rx', '3');
      
      svg.appendChild(rect);

      // إضافة قيمة فوق العمود
      if (day.count > 0) {
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', x + barWidth / 2);
        text.setAttribute('y', y - 5);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', '12');
        text.setAttribute('fill', '#666');
        text.textContent = day.count;
        svg.appendChild(text);
      }
    });
  }

  // رسم تسميات الأسبوع
  drawWeeklyLabels(svg, data) {
    data.forEach((day, index) => {
      const x = 50 + (index * 45) + 17.5; // وسط العمود
      
      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', x);
      text.setAttribute('y', '170');
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('font-size', '10');
      text.setAttribute('fill', '#666');
      text.textContent = day.dayName.substring(0, 3); // أول 3 أحرف
      svg.appendChild(text);
    });
  }

  // الحصول على لون العمود
  getBarColor(value, maxValue) {
    const intensity = value / maxValue;
    if (intensity >= 0.8) return '#4CAF50';
    if (intensity >= 0.6) return '#8BC34A';
    if (intensity >= 0.4) return '#CDDC39';
    if (intensity >= 0.2) return '#FFC107';
    return '#FF9800';
  }

  // الحصول على بيانات الأسبوع
  getWeeklyData() {
    const weekData = [];
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateString = date.toDateString();
      const dayName = dayNames[date.getDay()];
      
      const count = this.stats.dailyReads[dateString] || 0;
      
      weekData.push({
        date: dateString,
        dayName: dayName,
        count: count
      });
    }
    
    return weekData;
  }

  // تسجيل قراءة ذكر
  recordZikrRead(zikrId, category, readingTime = 30) {
    const today = new Date().toDateString();
    const now = new Date();
    const hour = now.getHours();

    // تحديث الإحصائيات اليومية
    this.stats.dailyReads[today] = (this.stats.dailyReads[today] || 0) + 1;

    // تحديث إحصائيات الفئات
    if (!this.stats.categoryStats[category]) {
      this.stats.categoryStats[category] = 0;
    }
    this.stats.categoryStats[category]++;

    // تحديث إحصائيات الوقت
    if (!this.stats.timeStats[hour]) {
      this.stats.timeStats[hour] = 0;
    }
    this.stats.timeStats[hour]++;

    // تحديث الأذكار المفضلة
    if (!this.stats.favoriteAzkar[zikrId]) {
      this.stats.favoriteAzkar[zikrId] = { count: 0, category: category };
    }
    this.stats.favoriteAzkar[zikrId].count++;

    // تحديث وقت القراءة الإجمالي
    this.stats.totalReadingTime += readingTime;

    // تحديث بيانات السلسلة
    this.updateStreakData(today);

    // حفظ الإحصائيات
    this.saveStatistics();

    // تحديث الواجهة
    this.updateStatisticsDisplay();
  }

  // تحديث بيانات السلسلة
  updateStreakData(today) {
    const lastReadDate = this.stats.streakData.lastReadDate;
    
    if (!lastReadDate) {
      // أول قراءة
      this.stats.streakData.current = 1;
      this.stats.streakData.longest = 1;
    } else {
      const lastDate = new Date(lastReadDate);
      const currentDate = new Date(today);
      const daysDiff = Math.floor((currentDate - lastDate) / (1000 * 60 * 60 * 24));
      
      if (daysDiff === 1) {
        // يوم متتالي
        this.stats.streakData.current++;
        if (this.stats.streakData.current > this.stats.streakData.longest) {
          this.stats.streakData.longest = this.stats.streakData.current;
        }
      } else if (daysDiff > 1) {
        // انقطعت السلسلة
        this.stats.streakData.current = 1;
      }
      // إذا كان daysDiff === 0، فهو نفس اليوم، لا نغير شيء
    }
    
    this.stats.streakData.lastReadDate = today;
  }

  // تحديث عرض الإحصائيات
  updateStatisticsDisplay() {
    // إجمالي القراءات
    const totalReads = Object.values(this.stats.dailyReads).reduce((sum, count) => sum + count, 0);
    document.getElementById('total-reads').textContent = totalReads;

    // السلسلة الحالية
    document.getElementById('current-streak').textContent = this.stats.streakData.current;

    // وقت القراءة
    const readingMinutes = Math.floor(this.stats.totalReadingTime / 60);
    document.getElementById('reading-time').textContent = readingMinutes;

    // عدد الإنجازات
    document.getElementById('achievements-count').textContent = this.stats.achievements.length;

    // تحديث الأذكار المفضلة
    this.updateFavoriteAzkarDisplay();

    // تحديث أنماط القراءة
    this.updateReadingPatternsDisplay();

    // تحديث الرسم البياني
    this.updateWeeklyChart();
  }

  // تحديث عرض الأذكار المفضلة
  updateFavoriteAzkarDisplay() {
    const container = document.getElementById('favorite-azkar-list');
    if (!container) return;

    const sortedAzkar = Object.entries(this.stats.favoriteAzkar)
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, 5); // أفضل 5

    container.innerHTML = sortedAzkar.map(([zikrId, data], index) => `
      <div class="favorite-item">
        <span class="rank">${index + 1}</span>
        <span class="category">${data.category}</span>
        <span class="count">${data.count} مرة</span>
      </div>
    `).join('');
  }

  // تحديث عرض أنماط القراءة
  updateReadingPatternsDisplay() {
    const container = document.getElementById('reading-patterns');
    if (!container) return;

    // العثور على أكثر الأوقات قراءة
    const bestHour = Object.entries(this.stats.timeStats)
      .sort(([,a], [,b]) => b - a)[0];

    const bestTime = bestHour ? this.formatHour(parseInt(bestHour[0])) : 'غير محدد';
    
    // حساب متوسط القراءات اليومية
    const totalDays = Object.keys(this.stats.dailyReads).length;
    const avgDaily = totalDays > 0 ? 
      (Object.values(this.stats.dailyReads).reduce((sum, count) => sum + count, 0) / totalDays).toFixed(1) : 0;

    container.innerHTML = `
      <div class="pattern-item">
        <span class="pattern-label">أفضل وقت للقراءة:</span>
        <span class="pattern-value">${bestTime}</span>
      </div>
      <div class="pattern-item">
        <span class="pattern-label">متوسط القراءات اليومية:</span>
        <span class="pattern-value">${avgDaily}</span>
      </div>
      <div class="pattern-item">
        <span class="pattern-label">أطول سلسلة:</span>
        <span class="pattern-value">${this.stats.streakData.longest} يوم</span>
      </div>
    `;
  }

  // تنسيق الساعة
  formatHour(hour) {
    if (hour >= 6 && hour < 12) return `${hour}:00 صباحاً`;
    if (hour >= 12 && hour < 18) return `${hour}:00 ظهراً`;
    if (hour >= 18 && hour < 24) return `${hour}:00 مساءً`;
    return `${hour}:00 ليلاً`;
  }

  // تحديث الإحصائيات
  updateStatistics() {
    this.updateStatisticsDisplay();
    
    // إظهار رسالة تأكيد
    const btn = document.getElementById('refresh-stats-btn');
    if (btn) {
      const originalText = btn.textContent;
      btn.textContent = 'تم التحديث ✓';
      btn.disabled = true;
      
      setTimeout(() => {
        btn.textContent = originalText;
        btn.disabled = false;
      }, 2000);
    }
  }

  // بدء جمع البيانات
  startDataCollection() {
    // الاستماع لأحداث قراءة الأذكار
    document.addEventListener('zikrRead', (event) => {
      const { zikrId, category } = event.detail;
      this.recordZikrRead(zikrId, category);
    });
  }

  // الحصول على تقرير مفصل
  generateDetailedReport() {
    const totalReads = Object.values(this.stats.dailyReads).reduce((sum, count) => sum + count, 0);
    const totalDays = Object.keys(this.stats.dailyReads).length;
    const avgDaily = totalDays > 0 ? (totalReads / totalDays).toFixed(1) : 0;
    
    return {
      summary: {
        totalReads,
        totalDays,
        avgDaily,
        currentStreak: this.stats.streakData.current,
        longestStreak: this.stats.streakData.longest,
        totalReadingTime: Math.floor(this.stats.totalReadingTime / 60)
      },
      categories: this.stats.categoryStats,
      timePatterns: this.stats.timeStats,
      favoriteAzkar: this.stats.favoriteAzkar,
      weeklyData: this.getWeeklyData()
    };
  }
}

// إنشاء مثيل عام
window.detailedStatisticsManager = new DetailedStatisticsManager();
