// مدير التأثيرات البصرية والرسوم المتحركة
class VisualEffectsManager {
  constructor() {
    this.particleContainer = null;
    this.progressChart = null;
    this.isAnimating = false;
  }

  // تهيئة التأثيرات البصرية
  init() {
    this.createParticleContainer();
    this.setupProgressChart();
    this.addGlowEffects();
  }

  // إنشاء حاوي الجسيمات
  createParticleContainer() {
    if (!this.particleContainer) {
      this.particleContainer = document.createElement('div');
      this.particleContainer.className = 'particle-container';
      this.particleContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 999;
        overflow: hidden;
      `;
      document.body.appendChild(this.particleContainer);
    }
  }

  // تأثير الجسيمات عند قراءة ذكر
  createReadingParticles(element) {
    if (!this.particleContainer) return;

    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // إنشاء 8 جسيمات
    for (let i = 0; i < 8; i++) {
      const particle = document.createElement('div');
      particle.className = 'reading-particle';
      
      const angle = (i / 8) * Math.PI * 2;
      const distance = 50 + Math.random() * 30;
      const endX = centerX + Math.cos(angle) * distance;
      const endY = centerY + Math.sin(angle) * distance;

      particle.style.cssText = `
        position: absolute;
        left: ${centerX}px;
        top: ${centerY}px;
        width: 6px;
        height: 6px;
        background: linear-gradient(45deg, #4CAF50, #81C784);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: particleFloat 1.5s ease-out forwards;
        --end-x: ${endX}px;
        --end-y: ${endY}px;
      `;

      this.particleContainer.appendChild(particle);

      // إزالة الجسيم بعد انتهاء الحركة
      setTimeout(() => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle);
        }
      }, 1500);
    }
  }

  // تأثير الاحتفال عند إنجاز
  createCelebrationEffect() {
    if (!this.particleContainer) return;

    // إنشاء 20 جسيم احتفالي
    for (let i = 0; i < 20; i++) {
      const particle = document.createElement('div');
      particle.className = 'celebration-particle';
      
      const startX = Math.random() * window.innerWidth;
      const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
      const color = colors[Math.floor(Math.random() * colors.length)];

      particle.style.cssText = `
        position: absolute;
        left: ${startX}px;
        top: -10px;
        width: 8px;
        height: 8px;
        background: ${color};
        border-radius: 50%;
        animation: celebrationFall ${2 + Math.random() * 2}s linear forwards;
        transform: rotate(${Math.random() * 360}deg);
      `;

      this.particleContainer.appendChild(particle);

      setTimeout(() => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle);
        }
      }, 4000);
    }
  }

  // إعداد الرسم البياني للتقدم
  setupProgressChart() {
    const chartContainer = document.getElementById('progress-chart-container');
    if (!chartContainer) return;

    // إنشاء رسم بياني بسيط بـ SVG
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '200');
    svg.setAttribute('viewBox', '0 0 300 200');
    svg.className = 'progress-chart';

    chartContainer.appendChild(svg);
    this.progressChart = svg;

    this.updateProgressChart();
  }

  // تحديث الرسم البياني
  async updateProgressChart() {
    if (!this.progressChart) return;

    try {
      // الحصول على بيانات آخر 7 أيام
      const weekData = await this.getWeeklyData();
      
      // مسح الرسم السابق
      this.progressChart.innerHTML = '';

      // رسم الخطوط والنقاط
      this.drawChartGrid();
      this.drawChartLine(weekData);
      this.drawChartPoints(weekData);
      this.drawChartLabels(weekData);

    } catch (error) {
      console.error('خطأ في تحديث الرسم البياني:', error);
    }
  }

  // رسم شبكة الرسم البياني
  drawChartGrid() {
    const svg = this.progressChart;
    
    // خطوط أفقية
    for (let i = 0; i <= 4; i++) {
      const y = 40 + (i * 30);
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', '40');
      line.setAttribute('y1', y);
      line.setAttribute('x2', '280');
      line.setAttribute('y2', y);
      line.setAttribute('stroke', '#e0e0e0');
      line.setAttribute('stroke-width', '1');
      svg.appendChild(line);
    }

    // خطوط عمودية
    for (let i = 0; i <= 6; i++) {
      const x = 40 + (i * 40);
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', x);
      line.setAttribute('y1', '40');
      line.setAttribute('x2', x);
      line.setAttribute('y2', '160');
      line.setAttribute('stroke', '#e0e0e0');
      line.setAttribute('stroke-width', '1');
      svg.appendChild(line);
    }
  }

  // رسم خط التقدم
  drawChartLine(data) {
    if (data.length < 2) return;

    const svg = this.progressChart;
    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    
    let pathData = '';
    data.forEach((point, index) => {
      const x = 40 + (index * 40);
      const y = 160 - (point.count * 3); // تحويل القيمة إلى موضع Y
      
      if (index === 0) {
        pathData += `M ${x} ${y}`;
      } else {
        pathData += ` L ${x} ${y}`;
      }
    });

    path.setAttribute('d', pathData);
    path.setAttribute('stroke', '#4CAF50');
    path.setAttribute('stroke-width', '3');
    path.setAttribute('fill', 'none');
    path.setAttribute('stroke-linecap', 'round');
    path.setAttribute('stroke-linejoin', 'round');
    
    svg.appendChild(path);
  }

  // رسم النقاط
  drawChartPoints(data) {
    const svg = this.progressChart;
    
    data.forEach((point, index) => {
      const x = 40 + (index * 40);
      const y = 160 - (point.count * 3);
      
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', x);
      circle.setAttribute('cy', y);
      circle.setAttribute('r', '4');
      circle.setAttribute('fill', '#4CAF50');
      circle.setAttribute('stroke', 'white');
      circle.setAttribute('stroke-width', '2');
      
      // إضافة تأثير hover
      circle.addEventListener('mouseenter', () => {
        circle.setAttribute('r', '6');
        this.showTooltip(x, y, point);
      });
      
      circle.addEventListener('mouseleave', () => {
        circle.setAttribute('r', '4');
        this.hideTooltip();
      });
      
      svg.appendChild(circle);
    });
  }

  // رسم التسميات
  drawChartLabels(data) {
    const svg = this.progressChart;
    
    data.forEach((point, index) => {
      const x = 40 + (index * 40);
      
      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', x);
      text.setAttribute('y', '180');
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('font-size', '10');
      text.setAttribute('fill', '#666');
      text.textContent = point.dayName;
      
      svg.appendChild(text);
    });
  }

  // الحصول على بيانات الأسبوع
  async getWeeklyData() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['readAzkar'], (result) => {
        const readAzkar = result.readAzkar || {};
        const weekData = [];
        
        const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        
        for (let i = 6; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          const dateString = date.toDateString();
          const dayName = dayNames[date.getDay()];
          
          const count = readAzkar[dateString] ? readAzkar[dateString].length : 0;
          
          weekData.push({
            date: dateString,
            dayName: dayName,
            count: Math.min(count, 40) // حد أقصى 40 للرسم
          });
        }
        
        resolve(weekData);
      });
    });
  }

  // عرض tooltip
  showTooltip(x, y, data) {
    const tooltip = document.createElement('div');
    tooltip.className = 'chart-tooltip';
    tooltip.innerHTML = `
      <div class="tooltip-content">
        <strong>${data.dayName}</strong><br>
        ${data.count} ذكر
      </div>
    `;
    
    tooltip.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y - 40}px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      pointer-events: none;
      z-index: 1000;
      transform: translateX(-50%);
    `;
    
    this.progressChart.parentElement.appendChild(tooltip);
  }

  // إخفاء tooltip
  hideTooltip() {
    const tooltip = document.querySelector('.chart-tooltip');
    if (tooltip) {
      tooltip.remove();
    }
  }

  // إضافة تأثيرات الإضاءة
  addGlowEffects() {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes particleFloat {
        0% {
          transform: translate(-50%, -50%) scale(1);
          opacity: 1;
        }
        100% {
          transform: translate(calc(var(--end-x) - 50%), calc(var(--end-y) - 50%)) scale(0);
          opacity: 0;
        }
      }
      
      @keyframes celebrationFall {
        0% {
          transform: translateY(-10px) rotate(0deg);
          opacity: 1;
        }
        100% {
          transform: translateY(100vh) rotate(720deg);
          opacity: 0;
        }
      }
      
      @keyframes glow {
        0%, 100% {
          box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
        }
        50% {
          box-shadow: 0 0 20px rgba(76, 175, 80, 0.8);
        }
      }
      
      .glow-effect {
        animation: glow 2s ease-in-out infinite;
      }
    `;
    
    document.head.appendChild(style);
  }

  // تطبيق تأثير الإضاءة على عنصر
  applyGlowEffect(element, duration = 3000) {
    element.classList.add('glow-effect');
    
    setTimeout(() => {
      element.classList.remove('glow-effect');
    }, duration);
  }

  // تأثير النبض للعداد
  pulseCounter(element) {
    element.style.animation = 'none';
    element.offsetHeight; // إعادة تشغيل الحركة
    element.style.animation = 'pulse 0.6s ease-in-out';
  }
}

// إنشاء مثيل عام
window.visualEffectsManager = new VisualEffectsManager();
